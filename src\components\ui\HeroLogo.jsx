import React, { useEffect, useRef } from 'react';
import logoImage from '../../images/LIVING-ONCOLOGY.png';

const HeroLogo = ({
  className = "w-full max-w-md mx-auto"
}) => {
  const logoRef = useRef(null);

  useEffect(() => {
    if (logoRef.current) {
      import('animejs').then(anime => {
        // Fix: anime.js v4+ exports anime directly, not as default
        const animeInstance = anime.default || anime;
        animeInstance({
          targets: logoRef.current,
          scale: [0.8, 1],
          opacity: [0, 1],
          duration: 1500,
          easing: 'easeOutElastic(1, .8)',
        });
      }).catch(error => {
        console.warn('Failed to load anime.js:', error);
      });
    }
  }, []);

  return (
    <div className={className}>
      <div
        ref={logoRef}
        className="w-full h-auto flex items-center justify-center bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 rounded-lg shadow-lg"
        style={{ minHeight: '200px' }}
      >
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">LIVING</h1>
          <h2 className="text-3xl font-semibold">ONCOLOGY</h2>
          <p className="text-sm mt-2 opacity-90">Advancing Cancer Care</p>
        </div>
      </div>
    </div>
  );
};

export default HeroLogo;
