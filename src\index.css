@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 29%; /* #4a4a4a - <PERSON> Gray */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 29%; /* #4a4a4a - <PERSON> Gray */
 
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 29%; /* #4a4a4a - Dark Gray */
 
    --primary: 20 88% 87%; /* #f8d5c2 - Primary Peach */
    --primary-foreground: 0 0% 29%; /* #4a4a4a - Dark Gray */
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 215.4 16.3% 46.9%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 16 80% 66%; /* #e98b67 - Accent Orange */
    --accent-foreground: 0 0% 100%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 85.1%; /* #d9d9d9 - Light Gray */
    --input: 0 0% 85.1%; /* #d9d9d9 - <PERSON> Gray */
    --ring: 16 80% 66%; /* #e98b67 - Accent Orange */
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 20 88% 87%;
    --primary-foreground: 0 0% 29%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 210 40% 98%;
 
    --accent: 16 80% 66%;
    --accent-foreground: 0 0% 100%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 16 80% 66%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #4a4a4a;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    color: #4a4a4a;
    font-weight: 600;
  }
}

@layer components {
  .btn-primary {
    @apply bg-accent hover:bg-accent/90 text-accent-foreground font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg;
  }

  .btn-secondary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg;
  }

  .skip-link {
    @apply absolute -top-10 left-4 bg-blue-600 text-white px-4 py-2 rounded focus:top-4 z-50 transition-all;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .gradient-overlay {
    background: linear-gradient(135deg, hsla(var(--primary), 0.9) 0%, hsla(var(--accent), 0.8) 100%);
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}