<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Component Test Results</h1>
        
        <div class="test-section">
            <h2>Anime.js Import Test</h2>
            <div id="anime-test-result" class="status info">Testing anime.js import...</div>
        </div>
        
        <div class="test-section">
            <h2>Logo Image Test</h2>
            <div id="image-test-result" class="status info">Testing logo image...</div>
            <img id="test-image" style="max-width: 200px; margin-top: 10px;" />
        </div>
        
        <div class="test-section">
            <h2>Animation Test</h2>
            <div id="animation-test-result" class="status info">Testing animation...</div>
            <div id="test-element" style="width: 100px; height: 100px; background: #007bff; margin: 10px 0; border-radius: 4px;"></div>
        </div>
    </div>

    <script type="module">
        // Test anime.js import
        async function testAnimeImport() {
            const resultDiv = document.getElementById('anime-test-result');
            try {
                const { animate } = await import('./node_modules/animejs/lib/anime.esm.js');
                if (typeof animate === 'function') {
                    resultDiv.className = 'status success';
                    resultDiv.textContent = '✅ Anime.js imported successfully!';
                    return animate;
                } else {
                    throw new Error('animate is not a function');
                }
            } catch (error) {
                resultDiv.className = 'status error';
                resultDiv.textContent = `❌ Failed to import anime.js: ${error.message}`;
                return null;
            }
        }

        // Test logo image
        function testLogoImage() {
            const resultDiv = document.getElementById('image-test-result');
            const testImage = document.getElementById('test-image');
            
            testImage.onload = () => {
                resultDiv.className = 'status success';
                resultDiv.textContent = '✅ Logo image loaded successfully!';
            };
            
            testImage.onerror = () => {
                resultDiv.className = 'status error';
                resultDiv.textContent = '❌ Failed to load logo image';
            };
            
            // Try to load the image from the src directory
            testImage.src = './src/images/LIVING-ONCOLOGY.png';
        }

        // Test animation
        async function testAnimation(animate) {
            const resultDiv = document.getElementById('animation-test-result');
            const testElement = document.getElementById('test-element');
            
            if (!animate) {
                resultDiv.className = 'status error';
                resultDiv.textContent = '❌ Cannot test animation - anime.js not available';
                return;
            }
            
            try {
                animate({
                    targets: testElement,
                    scale: [1, 1.2, 1],
                    duration: 1000,
                    ease: 'outElastic(1, .8)',
                });
                
                resultDiv.className = 'status success';
                resultDiv.textContent = '✅ Animation test completed successfully!';
            } catch (error) {
                resultDiv.className = 'status error';
                resultDiv.textContent = `❌ Animation test failed: ${error.message}`;
            }
        }

        // Run all tests
        async function runTests() {
            const animate = await testAnimeImport();
            testLogoImage();
            await testAnimation(animate);
        }

        // Start tests when page loads
        runTests();
    </script>
</body>
</html>
