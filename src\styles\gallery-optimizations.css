/* Photo Gallery Performance Optimizations */

/* Hardware acceleration for smooth animations */
.slideshow-container {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize image rendering */
.gallery-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: optimize-quality;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Touch optimization for mobile */
.touch-optimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Smooth scrolling for thumbnails */
.thumbnail-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.thumbnail-container::-webkit-scrollbar {
  display: none;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .slideshow-container,
  .gallery-image,
  .thumbnail-container {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .slideshow-container {
    aspect-ratio: 16/12; /* Slightly taller on mobile */
  }
  
  .gallery-controls {
    padding: 8px;
  }
  
  .thumbnail-container {
    padding: 0 16px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .gallery-image {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Loading state styles */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Focus styles for accessibility */
.gallery-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Prevent layout shift */
.aspect-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 62.5%; /* 16:10 aspect ratio */
}

.aspect-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
